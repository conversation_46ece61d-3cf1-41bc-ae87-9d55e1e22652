"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const footerItems: Array<{ name: string; href: string }> = [
  {
    name: "footerBase",
    href: "/components/sections/footers/footerBase",
  },
  {
    name: "footerBaseReveal",
    href: "/components/sections/footers/footerBaseReveal",
  },
  {
    name: "footerLogoEmphasisBackgroundGradient",
    href: "/components/sections/footers/footerLogoEmphasisBackgroundGradient",
  },
  {
    name: "footerLogoEmphasis",
    href: "/components/sections/footers/footerLogoEmphasis",
  },
  {
    name: "Mew Footer",
    href: "/components/sections/footers/mew",
  },
  {
    name: "Wallet Footer",
    href: "/components/sections/footers/wallet",
  },
];

export default function FootersPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {footerItems.map((item) => (
            <Link key={item.name} href={item.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{item.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
