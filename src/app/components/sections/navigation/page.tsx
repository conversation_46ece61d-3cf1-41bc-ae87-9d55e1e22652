"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const navbars: Array<{ name: string; href: string }> = [
  {
    name: "Minimal Navbar",
    href: "/components/sections/navigation/minimal-navbar",
  },
  {
    name: "Simple Navbar",
    href: "/components/sections/navigation/simple-navbar",
  },
  {
    name: "Simple Floating Navbar",
    href: "/components/sections/navigation/simple-floating-navbar",
  },
  {
    name: "Floating Navbar",
    href: "/components/sections/navigation/floating-navbar",
  },
];

export default function NavigationSectionsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {navbars.map((navbar) => (
            <Link key={navbar.name} href={navbar.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{navbar.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}